/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable react-hooks/exhaustive-deps */
import {
  hexToRgba,
  findTarget,
  findLongestSubarray,
  formatToPercentage,
  fillXAxisData,
} from '../utils'
import { defaultChartLineConfig, defaultLineSeries, defaultChartBarConfig } from '../config'
import type { ECharts, EChartsOption } from 'echarts'
import {
  CHART_TEXT_FONT_SIZE,
  CHART_TITLE_LEFT,
  CHART_TITLE_BOTTOM,
  CHART_TITLE_FONT_SIZE,
  CHART_AXIS_COLOR,
  CHART_LEGEND_BORDER_RADIUS,
  CHART_SYMBOL_CIRCLE,
  CHART_BAR_MAX_WIDTH,
  CHART_BAR_MIN_WIDTH,
  CHART_BAR_BORDER_RADIUS,
  CHART_SYMBOL_SIZE,
  CHART_SYMBOL_RECT,
  CHART_DATA_ZOOM_SLIDER_WIDTH,
  CHART_DATA_ZOOM_SLIDER_RIGHT,
  CHART_DATA_ZOOM_MAX_VALUE_SPAN,
  CHART_LINE_DASH,
  CHART_LINE_WIDTH,
  CHART_TEXT_BACKGROUND_COLOR,
  CHART_X_AXIS_MIN_VALUE,
  FIXED_LEGEND_ITEMS,
  TARGET_TEXT,
  WEEK_TEXT,
  DAY_TEXT,
  MONTH_TEXT,
  BOARD_NAME,
  CHART_ID_SEPARATOR,
  CHART_DEFAULT_Z_LEVEL,
  OPACITY,
  TEXT_PADDING_FACTOR,
  ERROR_MESSAGES,
  PLACEHOLDER_SERIES,
  BOARD_DISPLAY_NAMES,
} from '../constant'

export function getLineOption(data, ChartType, controlXText) {
  let { trendChartData, targetChallenge, target } = data || {} // TODO: 这里需再对齐下

  if (!trendChartData || !trendChartData?.length) {
    // 兼容服务端返回的数据格式不一致的问题
    const findData = findTarget(data, 'trendChartData')

    if (findData && findData?.length) {
      trendChartData = findData[0]
    } else {
      return {}
    }
  }

  try {
    const { chart } = ChartType.theme
    const legendData = trendChartData.map((item, index) => {
      return {
        name: item.name,
        icon: 'image://' + chart?.icons[index],
        itemStyle: {
          color: chart?.color[index] ?? '#000',
          borderRadius: [1, 1, 1, 1],
        },
        textStyle: { color: '#5F6A7A' },
      }
    })

    const findArr = trendChartData.map((item) => item.dataList)

    const xAxisData: number[] = findLongestSubarray(findArr).map((item) => item.index)

    // 对xAxisData排序后, 并保证前面从1开始补齐到最小值
    const newXAxisData = fillXAxisData(xAxisData)
    let dataArr: any[] = []

    const seriesData = trendChartData.map((item, index) => {
      const { dataList: itemDataList = [] } = item

      const showSymbol = itemDataList.filter((i) => typeof i?.rate === 'number').length === 1
      const data = itemDataList.map((item) => ({
        value: item.rate,
        fmNum: item.fmNum,
        fzNum: item.fzNum,
        isGeneral: ChartType.name === BOARD_NAME.GENERAL,
      }))

      dataArr = [...dataArr, ...data]
      const min = itemDataList[0]?.index

      for (let i = min; i > 1; i--) {
        data.unshift({
          value: undefined,
          fmNum: undefined,
          fzNum: undefined,
        })
      }

      const seriesItem = {
        ...defaultLineSeries,
        name: item.name,
        color: chart?.color[index] ?? '#000',
        data,
        showSymbol,
        // symbol: showSymbol ? 'circle' : undefined,
        // symbolSize: 8,
        lineStyle: {
          color: chart?.color[index] ?? '#000',
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                // color: chart?.color[index], // 起始颜色及透明度
                color: hexToRgba(chart?.color[index], OPACITY.HIGH),
              },
              {
                offset: 0.1,
                color: hexToRgba(chart?.color[index], OPACITY.MEDIUM),
              },
              {
                offset: 1,
                color: hexToRgba('#fff', OPACITY.LOW), // 结束颜色及透明度
              },
            ],
            global: false, // 缺省为 false
          },
        },
      }

      if (showSymbol) {
        ;(
          seriesItem as {
            symbol?: string
          }
        ).symbol = CHART_SYMBOL_CIRCLE
      }

      return seriesItem
    })

    if (seriesData?.length === 1) {
      seriesData[0].itemStyle = {
        width: 12,
        height: 12,
        color: chart?.color[0] ?? '#000',
        borderRadius: '50%',
      }
    }

    // 添加占位系列, 防止所有的线都隐藏的情况下, y轴消失
    seriesData.push(PLACEHOLDER_SERIES)

    const lastNum = controlXText[controlXText.length - 1]
    const titleText = lastNum > 30 ? WEEK_TEXT : lastNum > 12 ? DAY_TEXT : MONTH_TEXT
    const getYMax = () => {
      // 从对象数组中提取value值
      const dataValues = dataArr.map((item) => item?.value).filter(Boolean)

      // 如果target是数组，找出其中value最大的值；否则直接使用target值
      const targetMax = Array.isArray(target)
        ? Math.max(...target.map((item) => item.value))
        : target
      const targetChallengeMax = Array.isArray(targetChallenge)
        ? Math.max(...targetChallenge.map((item) => item.value))
        : targetChallenge

      return Math.max(targetChallengeMax, targetMax, ...dataValues)
    }
    if (Array.isArray(target)) {
      const chatIndex = 2
      legendData.push({
        name: '目标',
        icon: 'image://' + chart?.icons[chatIndex],
        itemStyle: {
          color: chart?.color[chatIndex] ?? '#000',
        },
      })
      seriesData.push({
        name: '目标',
        type: 'line',
        data: target.map((item) => item.value),
        // 虚线样式
        lineStyle: {
          type: 'dashed',
          color: chart?.color[chatIndex] ?? '#000',
        },
        // 不要符号点
        symbol: 'none',
        // 避免填充背景
        areaStyle: null,
        // 可以在该系列单独写 tooltip，也可以使用全局 tooltip
        tooltip: {
          show: true,
          // 如果想在该系列里自定义 tooltip 内容，可写 formatter 等配置
        },
      })
    }
    if (Array.isArray(targetChallenge)) {
      const chatIndex = 3
      legendData.push({
        name: '挑战',
        icon: 'image://' + chart?.icons[chatIndex],
        itemStyle: {
          color: chart?.color[chatIndex] ?? '#000',
        },
      })
      seriesData.push({
        name: '挑战',
        type: 'line',
        data: targetChallenge.map((item) => item.value),
        // 虚线样式
        lineStyle: {
          type: 'dashed',
          color: chart?.color[chatIndex] ?? '#000',
        },
        symbol: 'none',
        areaStyle: null,
        tooltip: {
          show: true,
        },
      })
    }

    console.log('ddd ==>seriesData', seriesData)
    return {
      ...defaultChartLineConfig,
      title: {
        ...defaultChartLineConfig.title,
        text: titleText, // 替换为实际单位
        left: CHART_TITLE_LEFT, // 左对齐
        bottom: CHART_TITLE_BOTTOM, // 距离底部的距离，可根据实际情况调整
        textStyle: {
          fontSize: CHART_TITLE_FONT_SIZE, // 字体大小
          fontWeight: 'normal', // 字体粗细
          color: CHART_AXIS_COLOR, // 字体颜色
        },
      },
      legend: {
        ...defaultChartLineConfig.legend,
        data: legendData,
        borderRadius: CHART_LEGEND_BORDER_RADIUS,
      },
      xAxis: {
        ...defaultChartLineConfig.xAxis,
        type: 'category',
        data: newXAxisData,
        offset: 2,
      },
      yAxis: {
        ...defaultChartLineConfig.yAxis,
        type: 'value',
        max: getYMax(),
        axisLabel: {
          formatter: (value) => {
            return formatToPercentage(value, 'label')
          },
        },
      },
      // 数据系列配置
      series: seriesData,
    }
  } catch (error) {
    console.log('🚀 ~ getOption ~ error:', error)

    return {}
  }
}
export function getBarOption({ barChartData }, ChartType): echarts.EChartsOption {
  const { chart } = ChartType.theme
  const legendData = FIXED_LEGEND_ITEMS.map((name, index) => {
    return {
      name,
      icon: 'image://' + chart?.icons[index],
      show: true,
      itemStyle: {
        color: chart?.color[index] ?? '#000',
        borderRadius: 1,
      },
      textStyle: { color: '#5F6A7A' },
    }
  })

  const defaultBarChartData = {
    xAxisData: [],
    seriesBarData: [],
    seriesLineData: [],
    specialData: {},
  }

  const { xAxisData, seriesBarData, seriesLineData, specialData } = barChartData
    ? barChartData.reduce((acc, item) => {
        const { name, rate, target, yoy, mom } = item
        acc.xAxisData.push(name)
        acc.seriesBarData.push(rate)
        acc.seriesLineData.push(target)
        if (ChartType.name === BOARD_NAME.SPECIAL) {
          acc.specialData.isSpecial = true
          acc.specialData[name.replace(BOARD_DISPLAY_NAMES.SPECIAL, '')] = [yoy, mom]
        }
        return acc
      }, defaultBarChartData)
    : defaultBarChartData
  const seriesLineDataLength = seriesLineData.length
  const endValue = seriesLineDataLength <= 10 ? 100 : (10 / seriesLineDataLength) * 100

  return {
    // 图表标题
    ...defaultChartBarConfig,
    legend: {
      ...defaultChartBarConfig.legend,
      data: legendData,
      show: true,
      borderRadius: CHART_LEGEND_BORDER_RADIUS,
    },
    // 横轴坐标轴
    xAxis: {
      ...defaultChartBarConfig.xAxis,
      type: 'value',
      splitNumber: 3,
    },
    // 纵轴坐标轴
    yAxis: {
      ...defaultChartBarConfig.yAxis,
      type: 'category',
      data: xAxisData,
    },
    // 系列列表，每个系列会根据 type 决定如何绘制
    series: [
      {
        name: FIXED_LEGEND_ITEMS[1], // 实际
        data: seriesBarData,
        color: chart?.color[1],
        barMaxWidth: CHART_BAR_MAX_WIDTH,
        barMinWidth: CHART_BAR_MIN_WIDTH,
        type: 'bar',
        barWidth: CHART_BAR_MAX_WIDTH,
        itemStyle: {
          borderRadius: CHART_BAR_BORDER_RADIUS,
        },
      },
      {
        name: FIXED_LEGEND_ITEMS[0], // 目标
        type: 'line',
        color: chart?.color[0],
        data: seriesLineData,
        showSymbol: true,
        symbolSize: CHART_SYMBOL_SIZE,
        symbol: CHART_SYMBOL_RECT,
        showAllSymbol: false,
        lineStyle: {
          width: 0,
          opacity: 0,
        },
      },
    ],
    dataZoom: [
      {
        type: 'slider',
        yAxisIndex: 0,
        filterMode: 'empty',
        start: 100,
        end: 100 - endValue,
        disabled: true,
        showDetail: false,
        backgroundColor: '#f5f7fa',
        borderRadius: CHART_DATA_ZOOM_SLIDER_WIDTH / 2,
        width: CHART_DATA_ZOOM_SLIDER_WIDTH,
        right: CHART_DATA_ZOOM_SLIDER_RIGHT,
        maxValueSpan: CHART_DATA_ZOOM_MAX_VALUE_SPAN,
        handleSize: CHART_DATA_ZOOM_SLIDER_WIDTH,
        handleIcon: `M256.5 32.4c-123.5 0-223.6 100.1-223.6 223.6s100.1 223.6 223.6 223.6 223.6-100.1 223.6-223.6S380 32.4 256.5 32.4z M206 157l0 199c0 0 1.45 8.2 9.9 8.2s9.55-8.2 9.55-8.2l0-199c0 0-1.02-8.7-9.55-8.7C207 148.3 206 157 206 157z M286.3 157l0 199c0 0 1.45 8.2 9.9 8.2s9.55-8.2 9.55-8.2l0-199c0 0-1.02-8.7-9.55-8.7C287.3 148.3 286.3 157 286.3 157z`,
        fillerColor: '#eceff2',
        brushSelect: false,
        zoomLock: false,
      },
      {
        type: 'inside',
        yAxisIndex: 0,
        zoomOnMouseWheel: false, // 禁用鼠标滚轮缩放
        moveOnMouseWheel: true, // 启用鼠标滚轮平移
        moveOnMouseMove: true, // 启用鼠标拖动平移
      },
    ],
    tooltip: {
      ...defaultChartLineConfig.tooltip,
      formatter: (params) => {
        return (
          defaultChartLineConfig.tooltip && (defaultChartLineConfig.tooltip as AnyType)
        ).formatter(params, specialData)
      },
    },
  }
}

export const getDefaultOption = (data, ChartType, controlXText): echarts.EChartsOption => {
  switch (ChartType.ChartType) {
    case 'line':
      return getLineOption(data, ChartType, controlXText)
    case 'bar':
      return getBarOption(data, ChartType)
    default:
      return {}
  }
}

// markine遇到兼容性问题，采用复杂的graphic实现虚线线段
export function getXlineOption({ ChartType, data }, chartInstance): EChartsOption['graphic'] {
  if (!chartInstance || !ChartType.extraLine?.length) {
    return []
  }

  const chartOption = chartInstance.getOption()

  if (!chartOption) {
    console.warn(ERROR_MESSAGES.CHART_RENDER_FAILED)
    return []
  }

  const graphic = ChartType.extraLine.reduce((curExtra, preExtra, index) => {
    const newExtra = [...curExtra]
    const targetValues = findTarget(data, preExtra.key).filter((item) => typeof item === 'number')

    targetValues.forEach((item) => {
      const [baseX, baseY] = (chartInstance as ECharts)?.convertToPixel('grid', [0, item]) ?? [0, 0]
      const { trendChartData } = data
      const maxLength = trendChartData.reduce((cur, pre) => {
        const { dataList } = pre
        if (dataList.length > cur) {
          cur = dataList.length
        }
        return cur
      }, 0)
      const [baseX1] = (chartInstance as ECharts)?.convertToPixel('grid', [
        maxLength - 1,
        item,
      ]) ?? [0, 0]

      const strY = `${baseY}`
      const preText = ChartType?.extraLine[index]?.text
      const showText = `${preText}: ${formatToPercentage(item, 'label')}`
      const z = ChartType?.extraLine[index]?.text === TARGET_TEXT ? 3 : 2
      const zlevel = CHART_DEFAULT_Z_LEVEL
      const fillColor = ChartType.theme.chart.color[index + 2]

      // 计算文本宽度，使用常量替代硬编码值
      const textWidth =
        TEXT_PADDING_FACTOR.SPACE_WIDTH * TEXT_PADDING_FACTOR.TEXT_FIXED_LENGTH +
        (showText.length - TEXT_PADDING_FACTOR.TEXT_FIXED_LENGTH) * TEXT_PADDING_FACTOR.CHAR_WIDTH +
        TEXT_PADDING_FACTOR.DEFAULT_PADDING

      newExtra.push(
        {
          type: 'line',
          zlevel,
          z,
          id: `${preText}${CHART_ID_SEPARATOR}line`,
          shape: {
            x1: baseX + textWidth,
            y1: strY,
            x2: baseX1,
            y2: strY,
          },
          style: {
            stroke: fillColor,
            lineDash: CHART_LINE_DASH,
            lineWidth: CHART_LINE_WIDTH,
          },
        },
        {
          type: 'text',
          id: `${preText}${CHART_ID_SEPARATOR}text`,
          x: baseX,
          y: baseY - 6,
          zlevel,
          z,
          style: {
            text: showText,
            fill: fillColor,
            fontSize: CHART_TEXT_FONT_SIZE,
            backgroundColor: CHART_TEXT_BACKGROUND_COLOR,
            padding: [2, 2],
          },
        }
      )
    })

    return newExtra
  }, [])

  console.log('ddd ==> graphic', graphic)
  return graphic
}

const getEchartsOption = ({
  data,
  currentBoardConfig,
  controlXText,
}): {
  option: echarts.EChartsOption
  graphic: any
} => {
  const getOption = () => {
    let updateOption = getDefaultOption(data, currentBoardConfig, controlXText)

    const { data: xAxisData, type } =
      (updateOption?.xAxis as EChartsOption['xAxis'] as {
        data: string[]
        type: string
      }) ?? {}

    console.log('ddd ==> updateOption', updateOption)

    if (xAxisData && xAxisData?.length > 12 && type !== 'time') {
      updateOption = {
        ...updateOption,
        xAxis: {
          ...(updateOption?.xAxis ?? {}),
          axisLabel: {
            interval: 0,
            formatter: function (value, index) {
              return controlXText.includes(index + 1) ? value : ''
            },
          },
        } as EChartsOption['xAxis'],
      }
    }
    if (currentBoardConfig.name === BOARD_NAME.SPECIAL) {
      // 让x轴的最大值不小于xAxisMaxValue 0.000003
      // 为了让横坐标至少有5个值
      const xAxisMaxValue = CHART_X_AXIS_MIN_VALUE
      let xAxisMax = 0
      if (Array.isArray(updateOption?.series)) {
        updateOption.series.forEach?.((item: any) => {
          item.data.forEach((i) => {
            if (i && typeof i === 'number' && i > xAxisMax) {
              xAxisMax = i
            }
          })
        })
      }

      if (updateOption?.xAxis && !Array.isArray(updateOption?.xAxis)) {
        const max = Math.max(xAxisMax, xAxisMaxValue)

        /**
         * 简化设置X轴刻度，确保只有5个刻度点
         * 通过设置min=0, max和interval来控制刻度数量
         */
        updateOption.xAxis.min = 0
        updateOption.xAxis.max = max

        // 计算interval以确保正好显示5个刻度值(0, 1/4*max, 2/4*max, 3/4*max, max)
        const interval = max / 4

        // 使用类型断言设置interval
        const xAxisAny = updateOption.xAxis as any
        xAxisAny.interval = interval

        // 移除可能造成干扰的配置
        delete xAxisAny.splitNumber
      }

      // 专项的y坐标, 可以去掉里面的专项
      const yAxisAny = updateOption.yAxis as any
      if (Array.isArray(yAxisAny?.data)) {
        yAxisAny.data = yAxisAny.data.map((i) => i?.replace(BOARD_DISPLAY_NAMES.SPECIAL, '') || '')
      }
    }

    return updateOption
  }

  return {
    option: getOption(),
    graphic: (childRef, { data, currentBoardConfig }) => {
      return getXlineOption({ data, ChartType: currentBoardConfig }, childRef)
    },
  }
}

export default getEchartsOption
