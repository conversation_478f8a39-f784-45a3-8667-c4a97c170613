import React, { memo, useState, useMemo } from 'react'
import { BOARD_LINE_STYLE } from '../constant'
import NavLine, { ActiveNavBar } from './NavLine'
import { iconMap } from '../config'
import { DimensionProp } from '../PrdQuality'
import '../index.scss'

export interface BaseDataProps {
  productLineName: string
  deviceTypes: string[]
  dimensions: DimensionProp[]
  defaultNavBar: ActiveNavBar
}

export interface ContentParams {
  activeNavBar: ActiveNavBar
  productLineName: string
  data: BaseDataProps
}

interface PrdBoardLineProps {
  data: BaseDataProps
  renderContent: (params: ContentParams) => React.ReactNode
}

const BoardLine = function ({ data, renderContent }: PrdBoardLineProps) {
  const { productLineName, deviceTypes, dimensions, defaultNavBar } = data

  const [activeNavBar, setActiveNavBar] = useState<ActiveNavBar>(defaultNavBar)

  const titleHandleLine = useMemo(() => {
    const NavBarProps = {
      deviceTypes,
      dimensions,
      activeNavBar,
      setActiveNavBar: (val: ActiveNavBar) => {
        console.log('ddd ==> setActiveNavBar', val)
        setActiveNavBar(val)
      },
    }
    return (
      <div
        className={`h-32 w-full flex justify-between items-center pl-8`}
        style={{ backgroundColor: BOARD_LINE_STYLE.TITLE_BACKGROUNDCOLOR }}
      >
        <div
          className="flex items-center justify-center"
          style={{
            fontWeight: BOARD_LINE_STYLE.TITLE_FONT_WEIGHT,
            fontSize: BOARD_LINE_STYLE.TITLE_FONT_SIZE,
            color: BOARD_LINE_STYLE.TITLE_COLOR,
          }}
        >
          <img
            src={iconMap[productLineName]}
            alt={productLineName}
            style={{
              width: BOARD_LINE_STYLE.ICON_SIZE,
              height: BOARD_LINE_STYLE.ICON_SIZE,
              alignSelf: 'center',
              marginRight: BOARD_LINE_STYLE.ICON_MARGIN_RIGHT,
            }}
          />
          <span>{productLineName}</span>
        </div>

        <NavLine {...NavBarProps} />
      </div>
    )
  }, [productLineName, deviceTypes, dimensions, activeNavBar, setActiveNavBar])

  const contentParams: ContentParams = {
    activeNavBar,
    productLineName,
    data,
  }

  return (
    <div className="rounded-6 mb-6 prd-boardline">
      {titleHandleLine}
      <div className="w-full bg-white rounded-6 px-8">{renderContent(contentParams)}</div>
    </div>
  )
}

export default memo(BoardLine)
