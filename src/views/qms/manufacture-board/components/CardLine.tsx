import React, { memo } from 'react'
import styled from 'styled-components'
import Tooltip from '@hi-ui/tooltip'
import { InfoCircleOutlined } from '@hi-ui/icons'
import { FONT_WEIGHT_BOLD, FONT_SIZE_MEDIUM, CARD_STYLE } from '../constant'
import { CARD_MAX_WIDTH } from '@/views/qms/billboard/constant'
import { bigFieldIcon as BigFieldIcon } from '@/views/qms/billboard/assets'

interface CardLineProps {
  title: string
  tooltip?: string
  showIcon?: boolean
  children?: React.ReactNode
}

const TitleWapper = styled.div`
  padding-top: 12px;
  padding-bottom: 12px;
  font-weight: ${FONT_WEIGHT_BOLD};
  font-size: ${FONT_SIZE_MEDIUM};
  color: ${CARD_STYLE.TITLE_COLOR};
`
export const showTooltip = (tooltip) => {
  return <div style={{ maxWidth: CARD_MAX_WIDTH, whiteSpace: 'wrap' }}>{tooltip}</div>
}

export const IconTooltip = memo((props: { text: string }) => {
  return (
    <Tooltip title={showTooltip(props.text)} trigger="hover">
      <InfoCircleOutlined
        style={{ fontSize: '16px', marginLeft: '4px', color: '#5F6A7A' }}
      ></InfoCircleOutlined>
    </Tooltip>
  )
})
IconTooltip.displayName = 'IconTooltip'

const CardLine: React.FC<CardLineProps> = function (props) {
  const { title, tooltip, showIcon = true } = props
  return (
    <TitleWapper className="flex justify-between items-center">
      <div className="flex justify-start items-center">
        {showIcon && <BigFieldIcon />}
        <span className="ml-2">{title}</span>
        {tooltip && <IconTooltip text={tooltip} />}
      </div>
      {props.children}
    </TitleWapper>
  )
}

export default CardLine
