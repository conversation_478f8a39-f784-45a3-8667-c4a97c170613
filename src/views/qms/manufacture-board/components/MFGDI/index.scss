$text-color: #1f2733 !default;
$text-color-active: #237ffa !default;
$font-weight-500: 500 !default;
$font-weight-400: 400 !default;
$font-weight-600: 600 !default;
$font-size-24: 24px !default;
$font-size-14: 14px !default;
$font-size-12: 12px !default;

$bg-color-light: #f2f4f7 !default;
$color-success: #08a351 !default;
$color-fail: #ff5959 !default;
$color-secondary: #9299a6 !default;
$color-tertiary: #5f6a7a !default;
$color-disabled: #d1d1d1 !default;
$color-dashed: #dfe2e8 !default;

$font-family-base: misans-medium, 'PingFang SC', 'Microsoft YaHei', sans-serif !default;

.mfgdi {
  &-header {
    margin-bottom: 20px;
    padding-bottom: 20px;
    border-bottom: 1.5px dashed $color-dashed;

    .card-name {
      font-size: $font-size-14;
      font-weight: $font-weight-500;
      color: $text-color;
      line-height: 20px;
      margin-bottom: 4px;
    }

    .value-text {
      font-family: $font-family-base;
      font-size: $font-size-24;
      font-weight: $font-weight-600;
      color: $text-color;
      line-height: 28px;

      &-success {
        color: $color-success;
      }

      &-fail {
        color: $color-fail;
      }
    }

    .target-text {
      font-size: $font-size-14;
      font-weight: $font-weight-500;
      font-family: $font-family-base;
      line-height: $font-size-24;
      color: $color-tertiary;
    }
  }

  &-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
  }
}

// Grid布局样式
.grid-container {
  display: grid;
  grid-template-columns: 116px 1fr 1fr;
  gap: 16px;
}

// 左侧统计卡片区域
.stats-cards {
  display: grid;
  grid-template-rows: 1fr 1fr;
  gap: 16px;
  height: 100%;
}

.stat-card {
  padding: 12px 16px !important;

  .stat-title {
    font-size: $font-size-14;
    font-weight: $font-weight-500;
    color: $text-color;
    line-height: 20px;
    margin-bottom: 8px;
  }

  .stat-value {
    font-family: $font-family-base;
    font-weight: $font-weight-600;
    color: $text-color;
    font-size: 20px;
    line-height: 28px;
  }
}

// 右侧图表区域
.chart-card {
  padding: 16px 20px !important;
  border-color: #e0ecff;
  background-color: #f4fbff66;

  .chart-title {
    font-size: $font-size-14;
    font-weight: $font-weight-500;
    color: $text-color;
    line-height: 24px;
    margin: 0 0 20px;
  }

  .chart-content {
    display: flex;
    flex: 1;
    gap: 16px;
    align-items: center;
    justify-content: space-between;
  }

  .pie-chart {
    flex: 2;
    height: 112px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

.pie-legend {
  flex: 1;
  min-width: 105px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.legend-item {
  font-size: $font-size-12;
  line-height: 16px;
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;

  .legend-color {
    width: 10px;
    height: 6px;
    border-radius: 2px;
  }

  .legend-label {
    flex: 1;
    color: $color-tertiary;
    font-weight: $font-weight-400;
    width: 100px;
  }

  .legend-value {
    color: $text-color;
    font-weight: $font-weight-500;
    min-width: 20px;
    text-align: right;
  }
}

.table-card {
  --hi-v4-spacing-5: 8px 10px;
}
