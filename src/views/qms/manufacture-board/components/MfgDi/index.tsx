import React, { memo, useEffect, useMemo, useRef, useState, useCallback } from 'react'
import cx from 'classnames'
import intl from 'react-intl-universal'
import EmptyState, { EMPTY_STATE_IMAGE_NO_DATA_COLOURFUL } from '@hi-ui/empty-state'
import EllipsisTooltip from '@hi-ui/ellipsis-tooltip'
import Popover from '@hi-ui/popover'
import Table from '@hi-ui/table'
import type { ContentParams } from '../BoardLine'
import { NavButtonText } from '../NavButton'
import { CardRadius, RiskTag, Divider, LevelTag, BaseTag } from '../Widgets'
import BoardChart from '../BoardChart'
import { BarChartData } from '../../interface'
import { formatFirstToUpperCase } from '../../utils'
import CardLine from '../CardLine'

import {
  PLACEHOLDER_STR,
  CARD_NAME,
  CARD_STYLE,
  MFG_DI_LEVEL,
  DATA_PRODUCTION_TIME_STYLE,
} from '../../constant'
import './index.scss'

interface MfgDiHeaderProps {
  cardName: string
  value: string
  target: string
  achieve: number
}

const MfgDiHeader = memo((props: MfgDiHeaderProps) => {
  const { cardName, value, target, achieve: propsAchieve } = props || {}
  const achieve = useMemo(() => {
    if (propsAchieve > 0) {
      return propsAchieve
    } else if (propsAchieve === 0 && (target ?? PLACEHOLDER_STR) === PLACEHOLDER_STR) {
      return -1
    }
    return propsAchieve
  }, [propsAchieve, target])
  return (
    <div className="mfgdi-header flex items-center gap-8">
      <div className="flex-1">
        <p className="card-name">{cardName}</p>
        <p className="flex items-center justify-between gap-8">
          <span
            className={cx('value-text', {
              'value-text-success': value !== PLACEHOLDER_STR && achieve > 0,
              'value-text-fail': value !== PLACEHOLDER_STR && achieve === 0,
            })}
          >
            {value ?? PLACEHOLDER_STR}
          </span>
          <span className="target-text">
            {'目标'}
            {target ?? PLACEHOLDER_STR}
          </span>
        </p>
      </div>
    </div>
  )
})
MfgDiHeader.displayName = 'MfgDiHeader'

interface PieLegendProps {
  list: BarChartData['chartData']['dataList']
  renderLabel?: (text: string) => string
}

const PieLegend = memo((props: PieLegendProps) => {
  const list = props.list || []
  const renderLabel = props.renderLabel || ((text) => text)

  let tiledLegends: BarChartData['chartData']['dataList'] = []
  let moreLegends: BarChartData['chartData']['dataList'] = []
  if (list.length > 5) {
    tiledLegends = list.slice(0, 3)
    moreLegends = list.slice(3)
  } else {
    tiledLegends = list
  }

  return (
    <div className="pie-legend">
      {tiledLegends.map((item, index) => {
        return (
          <div key={item.name + index} className="legend-item">
            <span className="legend-color" style={{ backgroundColor: item.color }} />
            <span className="legend-label">
              <EllipsisTooltip>{renderLabel(item.name)}</EllipsisTooltip>
            </span>
            <span className="legend-value">{item.value}</span>
          </div>
        )
      })}
      {moreLegends.length > 0 && (
        <Popover
          content={
            <div className="pie-legend">
              {moreLegends.map((item, index) => (
                <div key={item.name + index} className="legend-item">
                  <span className="legend-color" style={{ backgroundColor: item.color }} />
                  <span className="legend-label">
                    <EllipsisTooltip>{renderLabel(item.name)}</EllipsisTooltip>
                  </span>
                  <span className="legend-value">{item.value}</span>
                </div>
              ))}
            </div>
          }
          trigger="hover"
        >
          <div className="legend-item">
            <span className="legend-color" style={{ backgroundColor: '#9e9e9e' }} />
            <span className="legend-label">更多</span>
            <span className="legend-value">
              {moreLegends.reduce((acc, cur) => acc + cur.value, 0)}
            </span>
          </div>
        </Popover>
      )}
    </div>
  )
})
PieLegend.displayName = 'PieLegend'

const QualityMfgdi: React.FC<ContentParams> = ({ activeNavBar }) => {
  // mfg di
  const indexNodeList = ['TR4', 'TR5', 'TR6']
  const remainDays = 14
  const [trPhase, setTrPhase] = useState<string>(indexNodeList[indexNodeList.length - 1])
  const isEmpty = false
  const tooltipText =
    'MFG DI(Manufacturing Defect Index，生产制造缺陷率)是衡量生产质量的指标，将生产过程中未解决的问题按问题严重程度加权计算得到的量化质量缺陷的指标，DI=Blocker问题个数*10 + Critical问题个数*3 + Major问题个数*1 + Minor问题个数*0.1'
  const [tableColumns] = useState([
    {
      title: '问题ID',
      dataKey: 'issueId',
    },
    {
      title: '问题等级',
      dataKey: 'level',
      render: (text) => <LevelTag level={text}>{formatFirstToUpperCase(text)}</LevelTag>,
    },
    {
      title: '问题领域',
      dataKey: 'domain',
      render: (text) => <BaseTag>{text}</BaseTag>,
    },
    {
      title: '问题描述',
      dataKey: 'description',
    },
  ])
  const [tableDatas] = useState([
    {
      issueId: 'NBIBARL-410',
      level: 'blocker',
      domain: '软件',
      description: '问题的实际描述',
    },
    {
      issueId: 'NBIBARL-411',
      level: 'critical',
      domain: 'SQE',
      description: '问题的实际描述',
    },
    {
      issueId: 'NBIBARL-412',
      level: 'major',
      domain: 'PD_整机工艺',
      description: '问题的实际描述',
    },
    {
      issueId: 'NBIBARL-413',
      level: 'minor',
      domain: '软件',
      description: '问题的实际描述',
    },
  ])

  const boardName = CARD_NAME.MFG_DI
  const chartData: BarChartData = {
    chartData: {
      name: 'xxx',
      dataList: [
        {
          name: '1',
          value: 123,
        },
        {
          name: '1',
          value: 133,
        },
      ],
    },
  }

  const levelList = [
    { value: 735, name: 'blocker' },
    { value: 580, name: 'critical' },
    { value: 484, name: 'major' },
    { value: 500, name: 'minor' },
  ]
  const levelData: BarChartData = {
    chartData: {
      name: '未关闭问题级别分布',
      dataList: levelList.map((item) => {
        // 约束颜色及首字母大写
        return { ...item, ...MFG_DI_LEVEL[item.name], name: formatFirstToUpperCase(item.name) }
      }),
    },
  }

  const [domainList, setDomainList] = useState([
    { value: 735, name: '软件' },
    { value: 580, name: '整机工艺' },
    { value: 484, name: 'SQE' },
    { value: 200, name: '地方' },
    { value: 100, name: '短发' },
    { value: 210, name: '发的' },
    { value: 100, name: '高法' },
    { value: 50, name: '回答' },
  ])
  const domainData: BarChartData = {
    chartData: {
      name: '未关闭问题责任领域分布',
      dataList: domainList,
    },
  }

  const domainRef = useRef<null | { chartInstance: echarts.ECharts }>(null)
  const calcDomainLegendColors = useCallback(function () {
    console.log('ddd ==> domainRef-init', domainRef.current)
    if (!domainRef.current) return
    const instance = domainRef.current.chartInstance
    console.log('ddd ==> domainRef-option', instance.getOption())
    const { color } = instance.getOption()
    const includeColorDomains = domainList.map((item, index) => {
      return { ...item, color: (color as string[])[index] }
    })

    setDomainList(includeColorDomains)
  })
  useEffect(() => {
    const timer = setTimeout(() => {}, 300)

    return () => {
      clearTimeout(timer)
    }
  }, [domainRef, domainList])

  return (
    <>
      <div className="prd-boardcell">
        <CardLine title={CARD_NAME.MFG_DI} tooltip={tooltipText}>
          <div className="flex-1 flex justify-between items-center">
            <div
              className="flex items-center gap-4 ml-8"
              style={{
                color: DATA_PRODUCTION_TIME_STYLE.color,
                fontWeight: DATA_PRODUCTION_TIME_STYLE.fontWeight,
              }}
            >
              <span>{activeNavBar.project}</span>
              <Divider width="1px" height="12px" />
              <span>{activeNavBar.factory}</span>
              <Divider width="1px" height="12px" />
              <span>
                距离{trPhase}评审
                <strong style={{ color: CARD_STYLE.ACTIVE_COLOR }} className="font-bold ml-4 mr-4">
                  {remainDays}
                </strong>
                天
              </span>
              <RiskTag className="ml-4" risk="high">
                高风险
              </RiskTag>
            </div>
            <NavButtonText list={indexNodeList || []} setActive={setTrPhase} active={trPhase} />
          </div>
        </CardLine>
        <div className="flex gap-8">
          <CardRadius style={{ flex: '1 1 45%' }}>
            <MfgDiHeader cardName={boardName} target={'50'} value={'81'} achieve={1} />
            <div className="flex-1 w-full">
              {isEmpty ? (
                <EmptyState
                  className="flex items-center justify-center"
                  title={intl.get('暂无数据')}
                  indicator={EMPTY_STATE_IMAGE_NO_DATA_COLOURFUL}
                />
              ) : (
                <BoardChart
                  data={chartData}
                  currentBoardConfig={{
                    name: 'DI 达标',
                    ChartType: 'mfgdi-bar',
                  }}
                  controlXText={[]}
                  width="100%"
                  height="314px"
                  boardName={''}
                  lastPeriod={''}
                />
              )}
            </div>
          </CardRadius>
          <CardRadius style={{ flex: '1 1 55%' }}>
            <div className="grid-container">
              <div className="stats-cards">
                <CardRadius className="stat-card">
                  <h3 className="stat-title">问题总数</h3>
                  <div className="stat-value">16</div>
                </CardRadius>
                <CardRadius className="stat-card">
                  <h3 className="stat-title">问题关闭率</h3>
                  <div className="stat-value">89%</div>
                </CardRadius>
              </div>

              <CardRadius className="chart-card">
                <h3 className="chart-title">{levelData.chartData.name}</h3>
                <div className="chart-content">
                  <div className="pie-chart">
                    <BoardChart
                      data={levelData}
                      currentBoardConfig={{
                        name: levelData.chartData.name,
                        ChartType: 'mfgdi-pie',
                      }}
                      controlXText={[]}
                      width="112px"
                      height="112px"
                      boardName={''}
                      lastPeriod={''}
                    />
                  </div>
                  <PieLegend list={levelData.chartData.dataList} />
                </div>
              </CardRadius>

              <CardRadius className="chart-card">
                <h3 className="chart-title">{domainData.chartData.name}</h3>
                <div className="chart-content">
                  <div className="pie-chart">
                    <BoardChart
                      ref={domainRef}
                      data={domainData}
                      currentBoardConfig={{
                        name: domainData.chartData.name,
                        ChartType: 'mfgdi-pie',
                      }}
                      controlXText={[]}
                      width="112px"
                      height="112px"
                      boardName={''}
                      lastPeriod={''}
                    />
                  </div>
                  <PieLegend list={domainData.chartData.dataList} />
                </div>
              </CardRadius>
            </div>
            <div className="mt-10 table-card">
              <h3 className="table-title mb-8">未关闭问题明细</h3>
              <Table
                bordered
                size="sm"
                fieldKey="issueId"
                columns={tableColumns}
                data={tableDatas}
                maxHeight={120}
              />
            </div>
          </CardRadius>
        </div>
      </div>
    </>
  )
}
export default QualityMfgdi
