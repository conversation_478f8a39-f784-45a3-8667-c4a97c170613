import React from 'react'
import cx from 'classnames'

interface BaseButtonProps {
  list: string[]
  setActive: (val: string) => void
  active: string
}
interface NavBaseProps extends BaseButtonProps {
  className?: string
  itemClassName: string
  gap?: string
}

function NavBase({
  list = [],
  setActive,
  active,
  className = 'flex justify-start items-center',
  itemClassName,
  gap = 'gap-6',
}: NavBaseProps) {
  return (
    <div className={`${className} ${gap}`}>
      {list?.map((item) => (
        <div
          key={item}
          className={cx(itemClassName, { active: active === item })}
          onClick={() => setActive(item)}
        >
          {item}
        </div>
      ))}
    </div>
  )
}

export function NavButtonFilled(props: BaseButtonProps) {
  return <NavBase {...props} itemClassName="nav-button-filled" />
}
NavButtonFilled.displayName = 'NavButtonFilled'

export function NavButtonText(props: BaseButtonProps) {
  return <NavBase {...props} itemClassName="nav-button-text" gap="gap-10" />
}
NavButtonText.displayName = 'NavButtonText'
