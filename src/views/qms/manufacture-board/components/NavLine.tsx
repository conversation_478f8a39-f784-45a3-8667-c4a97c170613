/* eslint-disable react/display-name */
import React from 'react'
import styled from 'styled-components'
import Tooltip from '@hi-ui/tooltip'
import Select from '@hi-ui/select'
import { InfoCircleOutlined } from '@hi-ui/icons'
import { BOARD_LINE_STYLE } from '../constant'
import type { PrdBoardProps } from '../PrdQuality'
import { defaultTitleSvg } from '@/views/qms/billboard/components/NavLine'
import { Divider, NavButton } from './Widgets'

const NavBarContainer = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 10px;
  flex: 1;
`

export type ActiveNavBar = {
  deviceType: string
  region: string
  factory?: string
  project?: string
  date?: string
}

interface NavBarProps extends PrdBoardProps {
  activeNavBar: ActiveNavBar
  setActiveNavBar: (activeNavBar) => void
}

const NavLine: React.FC<NavBarProps> = ({
  deviceTypes,
  dimensions,
  activeNavBar,
  setActiveNavBar,
}) => {
  const dimensionsLength = dimensions.length
  const lastIndex = dimensionsLength - 1

  const handleClick = (type: Partial<ActiveNavBar>) => {
    setActiveNavBar({ ...activeNavBar, ...type })
  }

  return (
    <NavBarContainer>
      <div className="flex justify-start items-center">
        {Array.isArray(deviceTypes) &&
          deviceTypes.length > 1 &&
          deviceTypes.map((type, index) => (
            <NavButton
              key={`device-${type}-${index}`}
              active={activeNavBar.deviceType === type}
              onClick={() => handleClick({ deviceType: type })}
            >
              {defaultTitleSvg[type]?.({
                width: '16px',
                height: '16px',
              })}
              <span className="ml-3">{type}</span>
            </NavButton>
          ))}
      </div>
      <div className="flex">
        {dimensions.map((item, index) => {
          const list = item.list || []
          const tiledList = list.slice(0, 3)
          const moredList = list.slice(3).map((item) => ({ title: item, id: item }))
          const isActive = moredList.some((s) => s.id === activeNavBar[item.key])

          if (list.length === 0) return null
          return (
            <div key={item.key} className="flex justify-end items-center">
              {dimensionsLength > 1 && (
                <span
                  className="mr-3 flex items-center"
                  style={{
                    fontSize: BOARD_LINE_STYLE.LABEL_FONT_SIZE,
                    color: BOARD_LINE_STYLE.LABEL_COLOR,
                  }}
                >
                  {item.title}
                  {item?.tooltip && (
                    <Tooltip title={item.tooltip} trigger="hover">
                      <InfoCircleOutlined
                        style={{ fontSize: 16, marginLeft: 4, color: '#5F6A7A' }}
                      />
                    </Tooltip>
                  )}
                </span>
              )}
              {tiledList.map((s) => (
                <NavButton
                  key={s}
                  active={activeNavBar[item.key] === s}
                  onClick={() => handleClick({ [item.key]: s })}
                >
                  {s}
                </NavButton>
              ))}
              {moredList.length > 0 && (
                <Select
                  className={`prd-select-more ${isActive ? 'active' : ''}`}
                  data={moredList}
                  value={activeNavBar[item.key]}
                  clearable={false}
                  searchable
                  appearance="line"
                  style={{ width: '76px' }}
                  placeholder="更多"
                  optionWidth={160}
                  onChange={(value) => {
                    handleClick({ [item.key]: value })
                  }}
                />
              )}
              {index !== lastIndex && (
                <Divider margin={moredList.length > 0 ? '0 12px' : '0 12px 0 6px'} />
              )}
            </div>
          )
        })}
      </div>
    </NavBarContainer>
  )
}

export default NavLine
