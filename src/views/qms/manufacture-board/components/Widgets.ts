import styled from 'styled-components'
import { FONT_SIZE_NORMAL, TAG_BASE_COLOR, CARD_STYLE } from '../constant'

export const BaseTag = styled.div`
  padding: 2px 8px;
  font-weight: normal;
  border-radius: 4px;
  line-height: 20px;
  display: inline-block;
  font-size: ${FONT_SIZE_NORMAL};
  color: ${CARD_STYLE.TITLE_COLOR};
  background: ${TAG_BASE_COLOR};
`

const RISK_COLOR = {
  high: {
    color: '#B32D36',
    background: '#FEF1EE',
  },
  medium: {
    color: '#875100',
    background: '#FEFCE9',
  },
  low: {
    color: '#006BB3',
    background: '#ECFCFE',
  },
}
export const RiskTag = styled(BaseTag)<{ risk: keyof typeof RISK_COLOR }>`
  color: ${(props) => RISK_COLOR[props.risk]?.color};
  background: ${(props) => RISK_COLOR[props.risk]?.background};
`

const LEVEL_COLOR = {
  blocker: {
    color: '#FE7940',
    background: '#FFF0E9',
  },
  critical: {
    color: '#FE9561',
    background: '#FFF2EC',
  },
  major: {
    color: '#FEC833',
    background: '#FFF8E5',
  },
  minor: {
    color: '#FEE789',
    background: '#FFFBED',
  },
}
export const LevelTag = styled(BaseTag)<{ level: keyof typeof LEVEL_COLOR }>`
  color: ${(props) => LEVEL_COLOR[props.level]?.color};
  background: ${(props) => LEVEL_COLOR[props.level]?.background};
`

export const Divider = styled.div<{
  width?: string
  height?: string
  color?: string
  margin?: string
}>`
  flex: none;
  width: ${(props) => props.width || '2px'};
  height: ${(props) => props.height || '20px'};
  background-color: ${(props) => props.color || '#dfe2e8'};
  margin: ${(props) => props.margin || '0'};
`

export const NavButton = styled.div<{ active?: boolean }>`
  width: auto;
  height: 32px;
  display: flex;
  padding: 6px 12px;
  margin: 0 6px;
  justify-content: center;
  align-items: center;
  border-radius: 8px;
  border: ${(props) => (props.active ? '1px solid #BDE2FF' : '1px solid #ebedf0')};
  background: ${(props) => (props.active ? '#E2F3FE' : '#fff')};
  color: ${(props) => (props.active ? '#237ffa' : '#5f6a7a;')};
  font-weight: ${(props) => (props.active ? '500' : '400;')};
  text-align: center;
  font-feature-settings:
    'liga' off,
    'clig' off;
  font-family: 'PingFang SC';
  font-size: 14px;
  cursor: pointer;
  &:hover {
    color: #237ffa;
  }
`

export const CardRadius = styled.div`
  display: flex;
  flex-direction: column;
  border-radius: 12px;
  border: 1px solid #ebedf0;
  padding: 20px 16px;
`
