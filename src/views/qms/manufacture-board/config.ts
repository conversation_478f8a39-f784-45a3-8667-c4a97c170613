import { phoneIcon, laptopIcon, downIcon, upIcon } from '@/views/qms/billboard/assets'
import { effectStr, formatToPercentage } from '@/views/qms/billboard/utils'
import {
  CHART_TYPE,
  CHART_GRID_TOP,
  CHART_GRID_LEFT,
  CHART_GRID_RIGHT,
  CHART_GRID_BOTTOM,
  CHART_SPLIT_LINE_COLOR,
  CHART_AXIS_COLOR,
  CHART_TOOLTIP_COLOR,
  CHART_TOOLTIP_TITLE_COLOR,
  TOOLTIP_BORDER_RADIUS,
  TOOLTIP_SHADOW_COLOR,
  TOOLTIP_SHADOW_OFFSET_X,
  TOOLTIP_SHADOW_OFFSET_Y,
  TOOLTIP_SHADOW_BLUR,
  TOOLTIP_BORDER_WIDTH,
  TOOLTIP_BORDER_COLOR,
  GENERAL_YELLOW,
  GENERAL_BLUE,
  SPECIAL_YELLOW,
  SPECIAL_BLUE,
  KEY_PROJECTS_GREEN,
  KEY_PROJECTS_BLUE,
  THEME_COLORS,
  BOARD_NAME,
  EXTRA_LINE_CONFIG,
  SHOW_PERCENT,
  SHOW_LITTLE_ARR,
  PLACEHOLDER_STR,
  CHART_LEGEND_TOP,
  CHART_LEGEND_RIGHT,
  CHART_LEGEND_ITEM_WIDTH,
  CHART_LEGEND_ITEM_HEIGHT,
  CHART_LEGEND_ITEM_GAP,
  CHART_LEGEND_BORDER_RADIUS,
  CHART_BAR_RIGHT,
  DEFAULT_SHOW_INDEX,
  LIMIT_KEY_PROJECT,
  CHART_TOOLTIP_TITLE_FONT_SIZE,
  CHART_X_AXIS_MIN_VALUE,
  BOARD_TYPE,
  KEY_PROJECTS_RED,
  KEY_PROJECTS_ORANGE,
  TARGET_COLOR,
  CHALLENGE_COLOR,
  TARGET_TEXT,
  CHALLENGE_TEXT,
  INFINITY_SYMBOL,
  CARD_STYLE,
  NUMBER_CONVERSION,
  TRANSFORM_ROTATE,
  CHART_HOVER_COLOR,
} from '@/views/qms/billboard/constant'

import { DefaultTheme } from 'styled-components'
import { DIMENSION_TYPES } from './constant'

export const defaultLineSeries = {
  type: CHART_TYPE.LINE,
  lineStyle: {
    color: 'red',
  },
  // symbol: 'none',
  smooth: true,
}
const renderFzAndFm = (data) => {
  return data.isGeneral
    ? `<div style="font-size: 12px; text-align: left; margin-left: 18px; margin-top: 2px;">
      <span style="color: ${CHART_HOVER_COLOR};">${
        data.fmNum === undefined ? '-' : `${data.fzNum}/${data.fmNum}`
      }</span>
    </div>`
    : ''
}
const renderYoyAndMom = (data, yoyAndMomText) => {
  const num = parseFloat(data)
  const absValue = Math.abs(num)
  const isNegative = num > 0
  const isValidValue = effectStr(absValue) !== PLACEHOLDER_STR && data !== INFINITY_SYMBOL
  const isInfinitySymbol = data === INFINITY_SYMBOL
  return `<div style="display: flex; font-size: 12px; justify-content: space-between; align-items: center; width: auto; margin-top: 2px; margin-left: 10px;">
     <div style="flex: 1; display: flex; align-items: center; color: ${CHART_HOVER_COLOR}; margin: 0 14px 0 8px; text-align: left;">${yoyAndMomText}</div>
     <div style="display: flex; align-items: flex-end; flex: 2; color: ${CHART_TOOLTIP_TITLE_COLOR};font-weight: 600; text-align: right; margin-right: 14px;">
     <span style="margin-right: 2px; color: ${
       num !== 0
         ? !isValidValue || isInfinitySymbol
           ? ''
           : isNegative
             ? CARD_STYLE.DOWN_COLOR
             : CARD_STYLE.UP_COLOR
         : ''
     };">
      ${
        isInfinitySymbol
          ? INFINITY_SYMBOL
          : isValidValue
            ? `${(absValue * 100).toFixed(NUMBER_CONVERSION.DECIMAL_PLACES.STANDARD_PERCENT)}${
                NUMBER_CONVERSION.PERCENTAGE_SYMBOL
              }`
            : PLACEHOLDER_STR
      }
      </span>
       ${
         isValidValue && !isInfinitySymbol && num !== 0
           ? `
            <img
              src=${isNegative ? downIcon : upIcon}
              alt=${isNegative ? 'down' : 'up'}
              style="width: 14px; height: 14px; align-self: center; transform: ${
                num === 0 ? '' : TRANSFORM_ROTATE
              }; margin-bottom: ${isNegative ? 0 : 4}px;"
            />`
           : '-'
       }
     </div>
    </div>`
}
function tooltipFormatter(params, specialData) {
  let tooltipStr = '<div style="border-radius: 6px;padding: 0 4px;">'
  tooltipStr += `<p style="font-weight: 600; font-size: ${CHART_TOOLTIP_TITLE_FONT_SIZE}; color: ${CHART_TOOLTIP_TITLE_COLOR};">${params[0].axisValueLabel}</p>`
  const hasBar = params.some((item) => item.seriesType === CHART_TYPE.BAR)
  params.forEach((item, index) => {
    const { value, seriesName, data, color, name } = item
    let mergeColor = typeof color === 'string' ? color : color?.colorStops[0]?.color ?? 'black'
    if (!hasBar) {
      if (seriesName === TARGET_TEXT) {
        mergeColor = TARGET_COLOR
      }
      if (seriesName === CHALLENGE_TEXT) {
        mergeColor = CHALLENGE_COLOR
      }
    }
    tooltipStr += `<div style="display: flex; font-size: 12px; justify-content: space-between; align-items: center; width: auto; margin-top: 8px;">
     <div style="width: 10px; height: 6px; border-radius: 1px; margin-bottom: 2px; background-color: ${mergeColor}; align-self: center;"></div>
     <div style="flex: 1; display: flex; align-items: center; color: ${CHART_TOOLTIP_COLOR}; margin: 0 14px 0 8px; text-align: left;"> ${seriesName}</div>
     <div style="flex: 2; color: ${CHART_TOOLTIP_TITLE_COLOR};font-weight: 600; text-align: right; margin-right: 14px;">${formatToPercentage(
       value,
       'label'
     )}
     </div>
    </div>${
      specialData && specialData.isSpecial
        ? index === 0
          ? `${renderYoyAndMom(specialData[name]?.[0], '同比')}${renderYoyAndMom(
              specialData[name]?.[1],
              '环比'
            )}`
          : ''
        : renderFzAndFm(data)
    }`
  })
  tooltipStr += '</div>'
  return tooltipStr
}

export const defaultChartLineConfig: echarts.EChartsOption = {
  grid: {
    top: CHART_GRID_TOP,
    left: CHART_GRID_LEFT,
    right: CHART_GRID_RIGHT,
    bottom: CHART_GRID_BOTTOM,
    containLabel: true,
  },
  title: {
    text: '',
  },
  legend: {
    top: CHART_LEGEND_TOP,
    right: CHART_LEGEND_RIGHT,
    itemWidth: CHART_LEGEND_ITEM_WIDTH,
    itemHeight: CHART_LEGEND_ITEM_HEIGHT,
    itemGap: CHART_LEGEND_ITEM_GAP,
    textStyle: {
      color: CHART_TOOLTIP_COLOR,
      verticalAlign: 'middle',
      offset: [10, 0],
    },
    itemStyle: {
      borderRadius: CHART_LEGEND_BORDER_RADIUS,
    },
    lineStyle: {
      color: CHART_TOOLTIP_COLOR,
    },
  },
  xAxis: {
    type: 'category',
    axisLabel: {
      align: 'right',
    },
    boundaryGap: true,
    axisTick: { show: false },
    axisLine: {
      show: false,
      lineStyle: {
        color: CHART_AXIS_COLOR,
      },
    },
  },
  yAxis: {
    type: 'value',
    splitLine: {
      lineStyle: {
        color: CHART_SPLIT_LINE_COLOR,
      },
    },
    axisLine: {
      show: false,
      lineStyle: {
        color: CHART_AXIS_COLOR,
      },
    },
  },
  tooltip: {
    trigger: 'axis',
    borderRadius: TOOLTIP_BORDER_RADIUS,
    shadowColor: TOOLTIP_SHADOW_COLOR,
    shadowOffsetX: TOOLTIP_SHADOW_OFFSET_X,
    shadowOffsetY: TOOLTIP_SHADOW_OFFSET_Y,
    shadowBlur: TOOLTIP_SHADOW_BLUR,
    borderWidth: TOOLTIP_BORDER_WIDTH,
    borderColor: TOOLTIP_BORDER_COLOR,
    formatter: tooltipFormatter,
  },
}

export const defaultChartBarConfig: echarts.EChartsOption = {
  grid: {
    top: CHART_GRID_TOP,
    left: CHART_GRID_LEFT,
    right: CHART_BAR_RIGHT,
    bottom: CHART_GRID_BOTTOM - 2,
    containLabel: true,
  },
  legend: {
    show: true,
    top: CHART_LEGEND_TOP,
    right: CHART_LEGEND_RIGHT,
    itemWidth: CHART_LEGEND_ITEM_WIDTH,
    itemHeight: CHART_LEGEND_ITEM_HEIGHT,
    itemGap: CHART_LEGEND_ITEM_GAP,
    textStyle: {
      color: CHART_TOOLTIP_COLOR,
      verticalAlign: 'middle',
    },
    itemStyle: {
      borderRadius: CHART_LEGEND_BORDER_RADIUS,
    },
    lineStyle: {
      color: CHART_TOOLTIP_COLOR,
    },
  },
  // 横轴坐标轴
  xAxis: {
    boundaryGap: true,
    // min: 'dataMin',
    min: CHART_X_AXIS_MIN_VALUE,
    // max: 'dataMax',
    axisLine: {
      show: false,
      lineStyle: {
        color: CHART_AXIS_COLOR,
      },
    },
    axisTick: { show: false },
    axisLabel: {
      align: 'center',
      formatter: (value) => {
        return formatToPercentage(value, 'label')
      },
    },
  },
  // 纵轴坐标轴
  yAxis: {
    axisLine: {
      show: false,
      lineStyle: {
        color: CHART_AXIS_COLOR,
      },
    },
    type: 'category',
    boundaryGap: true,
    axisTick: { show: false },
    splitLine: {
      lineStyle: {
        color: CHART_SPLIT_LINE_COLOR,
      },
    },
  },
  tooltip: {
    trigger: 'axis',
    borderRadius: TOOLTIP_BORDER_RADIUS,
    shadowColor: TOOLTIP_SHADOW_COLOR,
    shadowOffsetX: TOOLTIP_SHADOW_OFFSET_X,
    shadowOffsetY: TOOLTIP_SHADOW_OFFSET_Y,
    shadowBlur: TOOLTIP_SHADOW_BLUR,
    borderWidth: TOOLTIP_BORDER_WIDTH,
    borderColor: TOOLTIP_BORDER_COLOR,
    formatter: tooltipFormatter,
  },
  // 系列列表，每个系列会根据 type 决定如何绘制
}

export const defaultBarSeries = {}

export interface ChartTheme {
  color: string[]
  icons: string[]
}

export interface AppTheme extends DefaultTheme {
  color: string
  background: string
  borderColor: string
  chart: ChartTheme
}

export const orangeTheme: AppTheme = {
  color: THEME_COLORS.ORANGE.COLOR,
  background: THEME_COLORS.ORANGE.BACKGROUND,
  borderColor: THEME_COLORS.ORANGE.BORDER_COLOR,
  chart: {
    color: THEME_COLORS.ORANGE.CHART_COLORS,
    icons: [GENERAL_YELLOW, GENERAL_BLUE, KEY_PROJECTS_RED, KEY_PROJECTS_ORANGE],
  },
}

export const blueTheme: AppTheme = {
  color: THEME_COLORS.BLUE.COLOR,
  borderColor: THEME_COLORS.BLUE.BORDER_COLOR,
  background: THEME_COLORS.BLUE.BACKGROUND,
  chart: {
    color: THEME_COLORS.BLUE.CHART_COLORS,
    icons: [SPECIAL_YELLOW, SPECIAL_BLUE],
  },
}

export const greenTheme: AppTheme = {
  color: THEME_COLORS.GREEN.COLOR,
  background: THEME_COLORS.GREEN.BACKGROUND,
  borderColor: THEME_COLORS.GREEN.BORDER_COLOR,
  chart: {
    color: THEME_COLORS.GREEN.CHART_COLORS,
    icons: [KEY_PROJECTS_GREEN, KEY_PROJECTS_BLUE, KEY_PROJECTS_GREEN, KEY_PROJECTS_BLUE],
  },
}

export const boardConfig = {
  [BOARD_TYPE.GENERAL]: {
    name: BOARD_NAME.GENERAL,
    theme: orangeTheme,
    ChartType: CHART_TYPE.LINE,
    extraLine: EXTRA_LINE_CONFIG,
  },
  [BOARD_TYPE.SPECIAL]: {
    name: BOARD_NAME.SPECIAL,
    theme: blueTheme,
    ChartType: CHART_TYPE.BAR,
  },
  [BOARD_TYPE.KEY_PROJECTS]: {
    name: BOARD_NAME.KEY_PROJECTS,
    theme: greenTheme,
    ChartType: CHART_TYPE.LINE,
  },
}

export const defaultShowIndex = DEFAULT_SHOW_INDEX

export const showLittleArr = SHOW_LITTLE_ARR
export const showPercent = SHOW_PERCENT

export const getLimitKeyProject = LIMIT_KEY_PROJECT

export const placeholderStr = PLACEHOLDER_STR

export const iconMap = {
  [DIMENSION_TYPES.PHONE.title]: phoneIcon,
  [DIMENSION_TYPES.FFR.title]: laptopIcon,
}
