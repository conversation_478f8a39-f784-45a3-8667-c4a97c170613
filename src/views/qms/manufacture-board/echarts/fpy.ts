export const getLineConfig = function (data) {
  console.log('ddd ==> fpy-getLineConfig', data)

  return {
    grid: {
      bottom: 6,
      containLabel: true,
      left: 2,
      right: 2,
      top: 40,
    },
    title: {
      text: '日',
      bottom: 1,
      left: 20,
      textStyle: {
        color: '#9299A6',
        fontSize: 10,
        fontWeight: 'normal',
      },
    },
    xAxis: {
      type: 'category',
      boundaryGap: true,
      offset: 2,
      axisTick: {
        show: false,
      },
      axisLine: {
        align: 'right',
      },
      data: ['6/14', '6/15', '6/16', '6/17', '6/18', '6/19', '6/20'],
    },
    yAxis: {
      type: 'value',
    },
    tooltip: {
      trigger: 'axis',
    },
    graphic: [
      {
        type: 'line',
        id: '目标--line',
        z: 3,
        zlevel: 2,
        style: {
          lineWidth: 1,
          stroke: '#FF7A75',
          lineDash: [5, 5],
        },
        shape: {
          x1: 122,
          x2: 423,
          y1: 40,
          y2: 40,
        },
      },
      {
        type: 'text',
        id: '目标--text',
        z: 3,
        zlevel: 2,
        style: {
          backgroundColor: '#F5F7FA',
          fill: '#FF7A75',
          fontSize: '10px',
          padding: [2, 2],
          text: '目标: 0.0581%',
        },
        x: 64.32906048114484,
        y: 34,
      },
    ],
    series: [
      {
        data: [820, 932, 901, 934, 1290, 1330, 1320],
        name: '实际',
        type: 'line',
        smooth: true,
        showSymbol: false,
        color: '#0189FF',
        lineStyle: {
          color: '#0189FF',
          width: 3,
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: 'rgba(1, 137, 255, 0.2)', // 起始颜色及透明度
              },
              {
                offset: 0.1,
                color: 'rgba(1, 137, 255, 0.1)',
              },
              {
                offset: 1,
                color: 'rgba(255,255,255, 0.05)', // 结束颜色及透明度
              },
            ],
            global: false, // 缺省为 false
          },
        },
      },
    ],
  }
}

export const getBarConfig = function (data) {
  console.log('ddd ==> fpy-getBarConfig', data)

  return {}
}
